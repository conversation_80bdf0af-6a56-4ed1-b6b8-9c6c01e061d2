# ESP32 Connection Verification Script

Write-Host "ESP32 Connection Verification" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green

# Check COM ports
Write-Host "`nAvailable COM ports:" -ForegroundColor Yellow
Get-WmiObject -Class Win32_SerialPort | Select-Object DeviceID, Description | Format-Table

# Check ESP32 device status
Write-Host "ESP32 Device Status:" -ForegroundColor Yellow
Get-PnpDevice | Where-Object {$_.FriendlyName -match "(CP210|ESP32|UART)"} | Select-Object FriendlyName, Status | Format-Table

# Instructions
Write-Host "✅ SOLUTION SUMMARY:" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
Write-Host "1. Your ESP32 is detected on COM8" -ForegroundColor White
Write-Host "2. In Arduino IDE, select:" -ForegroundColor White
Write-Host "   - Tools > Port > COM8 (Silicon Labs CP210x)" -ForegroundColor Cyan
Write-Host "   - Tools > Board > ESP32S3 Dev Module" -ForegroundColor Cyan
Write-Host "3. Upload your code normally" -ForegroundColor White
Write-Host "4. If timeout occurs, use BOOT+RESET sequence" -ForegroundColor White

Write-Host "`n🚫 DO NOT USE COM3 - That's Bluetooth!" -ForegroundColor Red
Write-Host "✅ USE COM8 - That's your ESP32!" -ForegroundColor Green

Write-Host "`nPress any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
