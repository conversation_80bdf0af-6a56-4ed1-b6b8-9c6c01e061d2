/*
 * Simple I2C Test for Deneyap Kart
 * Tests if I2C is working at all on your board
 */

#include <Wire.h>

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("\n=================================");
  Serial.println("🔍 SIMPLE I2C TEST");
  Serial.println("=================================");
  
  // Test standard I2C pins first
  Serial.println("Testing standard I2C pins (SDA:21, SCL:22)...");
  Wire.begin(21, 22);
  Wire.setClock(100000);
  delay(100);
  
  bool anyDeviceFound = false;
  
  Serial.println("Scanning I2C bus...");
  for (byte address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    byte error = Wire.endTransmission();
    
    if (error == 0) {
      Serial.printf("✅ Device found at address 0x%02X\n", address);
      anyDeviceFound = true;
    }
  }
  
  if (!anyDeviceFound) {
    Serial.println("❌ No I2C devices found on standard pins");
    Serial.println("\n💡 This suggests:");
    Serial.println("   - Camera is not connected");
    Serial.println("   - FFC cable issue");
    Serial.println("   - Hardware problem");
  } else {
    Serial.println("✅ I2C is working - camera should be detectable");
  }
  
  Wire.end();
  
  Serial.println("\n=================================");
  Serial.println("🔋 POWER CHECK:");
  Serial.println("1. Look at your camera module");
  Serial.println("2. Is there a small LED lit up?");
  Serial.println("3. Does it feel slightly warm?");
  Serial.println("4. Can you see the lens clearly?");
  Serial.println("=================================");
}

void loop() {
  // Blink to show test is complete
  digitalWrite(2, HIGH);
  delay(1000);
  digitalWrite(2, LOW);
  delay(1000);
  
  static unsigned long lastMsg = 0;
  if (millis() - lastMsg > 10000) {
    lastMsg = millis();
    Serial.println("💡 If no devices found, check camera connection!");
  }
}
