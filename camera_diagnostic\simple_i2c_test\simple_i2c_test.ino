/*
 * Simple I2C Test for Deneyap Kart
 * Tests if I2C is working at all on your board
 */

#include <Wire.h>

void setup() {
  Serial.begin(115200);
  delay(2000);

  Serial.println("\n=================================");
  Serial.println("🎥 CAMERA CONNECTION TEST");
  Serial.println("=================================");

  // Test the most likely pin combinations for Deneyap Kart
  int pinConfigs[][2] = {
    {21, 22}, // Standard ESP32
    {4, 5},   // Original Deneyap
    {18, 23}, // ESP32-S3
    {26, 27}  // ESP32-WROVER
  };

  bool cameraFound = false;

  for (int i = 0; i < 4; i++) {
    int sda = pinConfigs[i][0];
    int scl = pinConfigs[i][1];

    Serial.printf("Testing SDA:%d, SCL:%d... ", sda, scl);

    Wire.begin(sda, scl);
    Wire.setClock(100000);
    delay(100);

    // Check specifically for camera at 0x30
    Wire.beginTransmission(0x30);
    byte error = Wire.endTransmission();

    if (error == 0) {
      Serial.println("🎥 CAMERA FOUND!");
      cameraFound = true;
      Serial.printf("✅ Use these pins: SIOD_GPIO_NUM %d, SIOC_GPIO_NUM %d\n", sda, scl);
      break;
    } else {
      Serial.println("❌ No camera");
    }

    Wire.end();
    delay(100);
  }

  if (!cameraFound) {
    Serial.println("\n❌ Camera not detected on any pins");
    Serial.println("🔧 CHECK HARDWARE CONNECTION:");
    Serial.println("   1. FFC cable fully inserted?");
    Serial.println("   2. Black clip locked?");
    Serial.println("   3. Correct cable orientation?");
    Serial.println("   4. Cable damaged?");
  }

  Serial.println("\n=================================");
  Serial.println("🔋 POWER CHECK:");
  Serial.println("1. Look at your camera module");
  Serial.println("2. Is there a small LED lit up?");
  Serial.println("3. Does it feel slightly warm?");
  Serial.println("4. Can you see the lens clearly?");
  Serial.println("=================================");
}

void loop() {
  // Blink to show test is complete
  digitalWrite(2, HIGH);
  delay(1000);
  digitalWrite(2, LOW);
  delay(1000);

  static unsigned long lastMsg = 0;
  if (millis() - lastMsg > 10000) {
    lastMsg = millis();
    Serial.println("💡 If no devices found, check camera connection!");
  }
}
