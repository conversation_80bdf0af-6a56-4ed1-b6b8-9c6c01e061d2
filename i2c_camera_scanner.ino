/*
 * I2C Camera Scanner for Deneyap Kart
 * This will help identify the correct I2C pins for the camera
 */

#include <Wire.h>

// Different pin combinations to try
struct PinConfig {
  int sda;
  int scl;
  const char* name;
};

PinConfig pinConfigs[] = {
  {18, 23, "ESP32-S3 Config 1"},
  {26, 27, "ESP32-WROVER Config"},
  {4, 5, "Original Deneyap Config"},
  {21, 22, "Standard I2C"},
  {14, 15, "Alternative 1"},
  {16, 17, "Alternative 2"}
};

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("\n=================================");
  Serial.println("I2C Camera Scanner for Deneyap Kart");
  Serial.println("=================================");
  
  Serial.println("Scanning for camera on different I2C pin combinations...\n");
  
  int numConfigs = sizeof(pinConfigs) / sizeof(pinConfigs[0]);
  
  for (int i = 0; i < numConfigs; i++) {
    Serial.printf("Testing %s (SDA:%d, SCL:%d):\n", 
                  pinConfigs[i].name, pinConfigs[i].sda, pinConfigs[i].scl);
    
    Wire.begin(pinConfigs[i].sda, pinConfigs[i].scl);
    delay(100);
    
    bool found = false;
    
    // Scan I2C addresses
    for (byte address = 1; address < 127; address++) {
      Wire.beginTransmission(address);
      byte error = Wire.endTransmission();
      
      if (error == 0) {
        Serial.printf("  ✅ Device found at address 0x%02X\n", address);
        
        // Check if it's likely a camera (OV2640 typically at 0x30)
        if (address == 0x30) {
          Serial.println("  🎥 This looks like an OV2640 camera!");
        }
        found = true;
      }
    }
    
    if (!found) {
      Serial.println("  ❌ No devices found");
    }
    
    Wire.end();
    Serial.println();
    delay(500);
  }
  
  Serial.println("=================================");
  Serial.println("Scan completed!");
  Serial.println("If you found a device at 0x30, that's likely your camera.");
  Serial.println("Use the corresponding SDA/SCL pins in your camera configuration.");
  Serial.println("=================================");
}

void loop() {
  // Do nothing
  delay(1000);
}
