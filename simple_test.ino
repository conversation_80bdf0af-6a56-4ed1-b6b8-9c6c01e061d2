/*
 * Simple ESP32 Test - No Camera
 * This will help us verify basic ESP32 functionality
 */

#include <WiFi.h>
#include <WebServer.h>
#include "esp_task_wdt.h"

// WiFi credentials
const char* ssid = "THE ALPHA";
const char* password = "20011002";

// Web server on port 80
WebServer server(80);

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("\n=================================");
  Serial.println("ESP32 Simple Test - No Camera");
  Serial.println("=================================");
  
  // Initialize watchdog
  esp_task_wdt_init(30, true);
  esp_task_wdt_add(NULL);
  
  Serial.println("Step 1: Basic ESP32 test...");
  Serial.print("  🔧 Chip Model: ");
  Serial.println(ESP.getChipModel());
  Serial.print("  💾 Free Heap: ");
  Serial.println(ESP.getFreeHeap());
  Serial.print("  ⚡ CPU Frequency: ");
  Serial.print(ESP.getCpuFreqMHz());
  Serial.println(" MHz");
  
  esp_task_wdt_reset();
  
  Serial.println("Step 2: WiFi connection test...");
  connectToWiFi();
  
  esp_task_wdt_reset();
  
  Serial.println("Step 3: Web server setup...");
  setupWebServer();
  server.begin();
  
  Serial.println("✅ Test completed successfully!");
  if (WiFi.status() == WL_CONNECTED) {
    Serial.print("🌐 Visit: http://");
    Serial.println(WiFi.localIP());
  }
  Serial.println("=================================\n");
}

void loop() {
  esp_task_wdt_reset();
  server.handleClient();
  
  // Print status every 10 seconds
  static unsigned long lastStatus = 0;
  if (millis() - lastStatus > 10000) {
    lastStatus = millis();
    Serial.print("📊 Status - WiFi: ");
    Serial.print(WiFi.status() == WL_CONNECTED ? "Connected" : "Disconnected");
    Serial.print(", Free Heap: ");
    Serial.print(ESP.getFreeHeap());
    Serial.print(", Uptime: ");
    Serial.print(millis() / 1000);
    Serial.println("s");
  }
  
  delay(100);
}

void connectToWiFi() {
  Serial.println("  📡 Starting WiFi...");
  WiFi.begin(ssid, password);
  Serial.print("  🔄 Connecting");
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 30) {
    delay(500);
    Serial.print(".");
    attempts++;
    if (attempts % 10 == 0) {
      esp_task_wdt_reset();
    }
  }
  
  Serial.println();
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("  ✅ WiFi connected!");
    Serial.print("  📍 IP: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println("  ❌ WiFi failed");
  }
}

void setupWebServer() {
  server.on("/", []() {
    String html = "<html><body>";
    html += "<h1>ESP32 Test Page</h1>";
    html += "<p>✅ ESP32 is working!</p>";
    html += "<p>📡 WiFi: " + String(WiFi.status() == WL_CONNECTED ? "Connected" : "Disconnected") + "</p>";
    html += "<p>📍 IP: " + WiFi.localIP().toString() + "</p>";
    html += "<p>💾 Free Memory: " + String(ESP.getFreeHeap()) + " bytes</p>";
    html += "<p>⏱️ Uptime: " + String(millis() / 1000) + " seconds</p>";
    html += "</body></html>";
    server.send(200, "text/html", html);
  });
  
  server.on("/test", []() {
    server.send(200, "text/plain", "ESP32 Test OK!");
  });
}
