/*
 * Camera Pin Definitions for Deneyap Kart 1A v2
 *
 * This file contains the pin mappings for the Deneyap Camera (OV2640)
 * when connected to the Deneyap Kart 1A v2 via the FPC connector.
 *
 * The Deneyap Camera uses the OV2640 sensor and connects through
 * the dedicated camera connector on the back of the board.
 */

#ifndef CAMERA_PINS_H
#define CAMERA_PINS_H

// Camera pin definitions for Deneyap Kart (Original Configuration)
// Back to the original Deneyap Kart 1A v2 pins

#define PWDN_GPIO_NUM     -1  // Power down pin (not used)
#define RESET_GPIO_NUM    -1  // Reset pin (not used)
#define XCLK_GPIO_NUM     15  // External clock
#define SIOD_GPIO_NUM     4   // SDA for I2C communication
#define SIOC_GPIO_NUM     5   // SCL for I2C communication

#define Y9_GPIO_NUM       16  // Data bit 7
#define Y8_GPIO_NUM       17  // Data bit 6
#define Y7_GPIO_NUM       18  // Data bit 5
#define Y6_GPIO_NUM       12  // Data bit 4
#define Y5_GPIO_NUM       10  // Data bit 3
#define Y4_GPIO_NUM       8   // Data bit 2
#define Y3_GPIO_NUM       9   // Data bit 1
#define Y2_GPIO_NUM       11  // Data bit 0

#define VSYNC_GPIO_NUM    6   // Vertical sync
#define HREF_GPIO_NUM     7   // Horizontal reference
#define PCLK_GPIO_NUM     13  // Pixel clock

// Alternative pin definitions (if the above doesn't work)
// These are based on ESP32-S3 camera examples
/*
#define PWDN_GPIO_NUM     -1
#define RESET_GPIO_NUM    -1
#define XCLK_GPIO_NUM     4
#define SIOD_GPIO_NUM     18
#define SIOC_GPIO_NUM     23

#define Y9_GPIO_NUM       36
#define Y8_GPIO_NUM       37
#define Y7_GPIO_NUM       38
#define Y6_GPIO_NUM       39
#define Y5_GPIO_NUM       35
#define Y4_GPIO_NUM       14
#define Y3_GPIO_NUM       13
#define Y2_GPIO_NUM       34

#define VSYNC_GPIO_NUM    5
#define HREF_GPIO_NUM     27
#define PCLK_GPIO_NUM     25
*/

// Camera settings
#define CAMERA_MODEL_DENEYAP_OV2640

// Frame size options:
// FRAMESIZE_96X96,    // 96x96
// FRAMESIZE_QQVGA,    // 160x120
// FRAMESIZE_QCIF,     // 176x144
// FRAMESIZE_HQVGA,    // 240x176
// FRAMESIZE_240X240,  // 240x240
// FRAMESIZE_QVGA,     // 320x240
// FRAMESIZE_CIF,      // 400x296
// FRAMESIZE_HVGA,     // 480x320
// FRAMESIZE_VGA,      // 640x480
// FRAMESIZE_SVGA,     // 800x600
// FRAMESIZE_XGA,      // 1024x768
// FRAMESIZE_HD,       // 1280x720
// FRAMESIZE_SXGA,     // 1280x1024
// FRAMESIZE_UXGA,     // 1600x1200

// Default camera settings
#define DEFAULT_FRAME_SIZE FRAMESIZE_SVGA
#define DEFAULT_JPEG_QUALITY 12
#define DEFAULT_FB_COUNT 1

#endif // CAMERA_PINS_H
