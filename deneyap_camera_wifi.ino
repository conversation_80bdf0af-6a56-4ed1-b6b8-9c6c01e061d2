/*
 * Deneyap Kart Camera WiFi Project
 *
 * This code connects the Deneyap Kart to WiFi and sets up a camera web server
 * for live streaming and photo capture using the Deneyap Camera module.
 *
 * Hardware:
 * - Deneyap Kart (ESP32-WROVER based)
 * - Deneyap Camera (OV2640)
 *
 * WiFi Network: THE ALPHA
 * Password: 20012001
 */

#include "esp_camera.h"
#include <WiFi.h>
#include <WebServer.h>
#include <WiFiClient.h>
#include "esp_task_wdt.h"
#include "camera_pins.h"
#include "web_interface.h"

// WiFi credentials
const char* ssid = "THE ALPHA";
const char* password = "20011002";

// Web server on port 80
WebServer server(80);

// Camera configuration
camera_config_t config;

void setup() {
  Serial.begin(115200);
  delay(1000); // Give serial time to initialize
  Serial.println("\n=================================");
  Serial.println("Deneyap Kart Camera WiFi Project");
  Serial.println("=================================");

  // Add watchdog timer reset to prevent boot loops
  esp_task_wdt_init(30, true); // 30 second watchdog
  esp_task_wdt_add(NULL);

  Serial.println("Step 1: Initializing camera...");
  esp_task_wdt_reset(); // Reset watchdog

  // Initialize camera with error handling
  if (initCamera()) {
    Serial.println("✅ Camera initialized successfully");
  } else {
    Serial.println("❌ Camera initialization failed");
    Serial.println("Continuing without camera...");
    // Don't return, continue with WiFi setup
  }

  Serial.println("Step 2: Connecting to WiFi...");
  esp_task_wdt_reset(); // Reset watchdog

  // Connect to WiFi
  connectToWiFi();

  Serial.println("Step 3: Setting up web server...");
  esp_task_wdt_reset(); // Reset watchdog

  // Setup web server routes
  setupWebServer();

  // Start web server
  server.begin();
  Serial.println("✅ Web server started successfully");

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\n🎉 Setup completed successfully!");
    Serial.print("📱 Camera Stream URL: http://");
    Serial.print(WiFi.localIP());
    Serial.println("/");
    Serial.print("📶 WiFi Signal: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
  } else {
    Serial.println("⚠️  Setup completed but WiFi not connected");
    Serial.println("📱 Access Point mode or manual WiFi setup may be needed");
  }

  Serial.println("=================================\n");
}

void loop() {
  // Reset watchdog timer
  esp_task_wdt_reset();

  // Handle web server requests
  server.handleClient();

  // Check WiFi connection status every 30 seconds
  static unsigned long lastWiFiCheck = 0;
  if (millis() - lastWiFiCheck > 30000) {
    lastWiFiCheck = millis();

    if (WiFi.status() != WL_CONNECTED) {
      Serial.println("⚠️  WiFi connection lost. Attempting to reconnect...");
      connectToWiFi();
    }
  }

  // Small delay to prevent watchdog issues
  delay(10);
}

bool initCamera() {
  Serial.println("  📷 Scanning for camera on FFC connector...");

  // Comprehensive I2C pin combinations to test
  // Since FFC connector pins are fixed, one of these should work
  struct {
    int sda;
    int scl;
    const char* name;
  } pinCombinations[] = {
    {26, 27, "ESP32-WROVER Standard"},
    {21, 22, "ESP32 Standard I2C"},
    {18, 23, "ESP32-S3 Configuration"},
    {14, 15, "Alternative Config 1"},
    {16, 17, "Alternative Config 2"},
    {13, 14, "Alternative Config 3"},
    {12, 13, "Alternative Config 4"},
    {19, 20, "Alternative Config 5"},
    {32, 33, "Alternative Config 6"},
    {25, 26, "ESP32-CAM Style"},
    {2, 15, "Custom Config 1"},
    {0, 2, "Custom Config 2"}
  };

  int numConfigs = sizeof(pinCombinations) / sizeof(pinCombinations[0]);
  bool cameraFound = false;

  Serial.printf("  🔍 Testing %d different I2C pin combinations...\n", numConfigs);

  for (int i = 0; i < numConfigs; i++) {
    Serial.printf("  🔄 Test %d/%d: %s (SDA:%d, SCL:%d)\n",
                  i+1, numConfigs, pinCombinations[i].name,
                  pinCombinations[i].sda, pinCombinations[i].scl);

    // Camera configuration
    config.ledc_channel = LEDC_CHANNEL_0;
    config.ledc_timer = LEDC_TIMER_0;
    config.pin_d0 = Y2_GPIO_NUM;
    config.pin_d1 = Y3_GPIO_NUM;
    config.pin_d2 = Y4_GPIO_NUM;
    config.pin_d3 = Y5_GPIO_NUM;
    config.pin_d4 = Y6_GPIO_NUM;
    config.pin_d5 = Y7_GPIO_NUM;
    config.pin_d6 = Y8_GPIO_NUM;
    config.pin_d7 = Y9_GPIO_NUM;
    config.pin_xclk = XCLK_GPIO_NUM;
    config.pin_pclk = PCLK_GPIO_NUM;
    config.pin_vsync = VSYNC_GPIO_NUM;
    config.pin_href = HREF_GPIO_NUM;
    config.pin_sscb_sda = pinCombinations[i].sda;  // Test this SDA
    config.pin_sscb_scl = pinCombinations[i].scl;  // Test this SCL
    config.pin_pwdn = PWDN_GPIO_NUM;
    config.pin_reset = RESET_GPIO_NUM;
    config.xclk_freq_hz = 20000000;
    config.pixel_format = PIXFORMAT_JPEG;

    // Set frame size (only on first iteration)
    if (i == 0) {
      Serial.print("  💾 PSRAM: ");
      if(psramFound()){
        Serial.println("Available");
        config.frame_size = FRAMESIZE_VGA;
        config.jpeg_quality = 15;
        config.fb_count = 2;
      } else {
        Serial.println("Not found");
        config.frame_size = FRAMESIZE_QVGA;
        config.jpeg_quality = 20;
        config.fb_count = 1;
      }
    }

    // Reset watchdog
    esp_task_wdt_reset();

    // Try to initialize camera
    esp_err_t err = esp_camera_init(&config);

    if (err == ESP_OK) {
      // Test if camera actually responds
      camera_fb_t * fb = esp_camera_fb_get();
      if (fb) {
        Serial.printf("  🎉 CAMERA FOUND! %s works!\n", pinCombinations[i].name);
        Serial.printf("  📍 Correct pins: SDA=%d, SCL=%d\n",
                      pinCombinations[i].sda, pinCombinations[i].scl);
        Serial.printf("  📸 Test capture: %d bytes\n", fb->len);
        esp_camera_fb_return(fb);
        cameraFound = true;
        break;  // Success! Stop testing
      } else {
        Serial.println("    ⚠️  Init OK but capture failed");
        esp_camera_deinit();
      }
    } else {
      Serial.printf("    ❌ Failed (0x%x)\n", err);
    }

    delay(100);  // Brief delay between tests
  }

  if (!cameraFound) {
    Serial.println("  💥 Camera not found on any I2C pin combination!");
    Serial.println("  🔧 This suggests a hardware issue:");
    Serial.println("     - FFC cable not properly connected");
    Serial.println("     - Wrong cable orientation");
    Serial.println("     - Damaged cable or camera module");
    Serial.println("     - Power supply issue");
    return false;
  }

  // Check if any configuration worked
  sensor_t * s = esp_camera_sensor_get();
  if (s == NULL) {
    Serial.println("  💥 All configurations failed!");
    Serial.println("  🔧 Hardware troubleshooting needed:");
    Serial.println("     - Check FFC cable orientation (try flipping it)");
    Serial.println("     - Ensure cable is fully inserted and locked");
    Serial.println("     - Verify camera module is not damaged");
    Serial.println("     - Check for loose connections");
    return false;
  }

  Serial.println("  ✅ Camera hardware initialized");

  // Configure camera sensor settings
  if (s != NULL) {
    // Initial sensor settings
    s->set_brightness(s, 0);     // -2 to 2
    s->set_contrast(s, 0);       // -2 to 2
    s->set_saturation(s, 0);     // -2 to 2
    s->set_special_effect(s, 0); // 0 to 6 (0-No Effect, 1-Negative, 2-Grayscale, 3-Red Tint, 4-Green Tint, 5-Blue Tint, 6-Sepia)
    s->set_whitebal(s, 1);       // 0 = disable , 1 = enable
    s->set_awb_gain(s, 1);       // 0 = disable , 1 = enable
    s->set_wb_mode(s, 0);        // 0 to 4 - if awb_gain enabled (0 - Auto, 1 - Sunny, 2 - Cloudy, 3 - Office, 4 - Home)
    s->set_exposure_ctrl(s, 1);  // 0 = disable , 1 = enable
    s->set_aec2(s, 0);           // 0 = disable , 1 = enable
    s->set_ae_level(s, 0);       // -2 to 2
    s->set_aec_value(s, 300);    // 0 to 1200
    s->set_gain_ctrl(s, 1);      // 0 = disable , 1 = enable
    s->set_agc_gain(s, 0);       // 0 to 30
    s->set_gainceiling(s, (gainceiling_t)0);  // 0 to 6
    s->set_bpc(s, 0);            // 0 = disable , 1 = enable
    s->set_wpc(s, 1);            // 0 = disable , 1 = enable
    s->set_raw_gma(s, 1);        // 0 = disable , 1 = enable
    s->set_lenc(s, 1);           // 0 = disable , 1 = enable
    s->set_hmirror(s, 0);        // 0 = disable , 1 = enable
    s->set_vflip(s, 0);          // 0 = disable , 1 = enable
    s->set_dcw(s, 1);            // 0 = disable , 1 = enable
    s->set_colorbar(s, 0);       // 0 = disable , 1 = enable
  }

  return true;
}

void connectToWiFi() {
  Serial.println("  📡 Starting WiFi connection...");
  Serial.print("  🌐 Network: ");
  Serial.println(ssid);

  // Disconnect any previous connection
  WiFi.disconnect();
  delay(100);

  // Start connection
  WiFi.begin(ssid, password);
  Serial.print("  🔄 Connecting");

  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 30) { // Increased timeout
    delay(500);
    Serial.print(".");
    attempts++;

    // Reset watchdog during connection
    if (attempts % 10 == 0) {
      esp_task_wdt_reset();
    }
  }

  Serial.println(); // New line after dots

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("  ✅ WiFi connected successfully!");
    Serial.print("  📍 IP address: ");
    Serial.println(WiFi.localIP());
    Serial.print("  📶 Signal strength: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
    Serial.print("  🔒 Encryption: ");
    Serial.println(WiFi.encryptionType(0));
  } else {
    Serial.println("  ❌ Failed to connect to WiFi");
    Serial.println("  💡 Please check:");
    Serial.println("     - Network name (SSID) is correct");
    Serial.println("     - Password is correct");
    Serial.println("     - Router is within range");
    Serial.println("     - Router is broadcasting SSID");
  }
}

void setupWebServer() {
  // Main page
  server.on("/", handleRoot);

  // Camera stream
  server.on("/stream", handleStream);

  // Capture photo
  server.on("/capture", handleCapture);

  // Camera settings
  server.on("/settings", handleSettings);

  // Status page
  server.on("/status", handleStatus);

  // 404 handler
  server.onNotFound(handleNotFound);
}

void handleRoot() {
  server.send(200, "text/html", getMainPage());
}

void handleStream() {
  WiFiClient client = server.client();

  // Check if camera is available
  sensor_t * s = esp_camera_sensor_get();
  if (s == NULL) {
    server.send(503, "text/plain", "Camera not available");
    return;
  }

  String response = "HTTP/1.1 200 OK\r\n";
  response += "Content-Type: multipart/x-mixed-replace; boundary=frame\r\n\r\n";
  server.sendContent(response);

  int frameCount = 0;
  while (client.connected() && frameCount < 1000) { // Limit frames to prevent memory issues
    esp_task_wdt_reset(); // Reset watchdog during streaming

    camera_fb_t * fb = esp_camera_fb_get();
    if (!fb) {
      Serial.println("⚠️  Camera capture failed");
      delay(100); // Brief delay before retry
      continue;
    }

    if (fb->format != PIXFORMAT_JPEG) {
      Serial.println("⚠️  Non-JPEG data not implemented");
      esp_camera_fb_return(fb);
      break;
    }

    String header = "--frame\r\n";
    header += "Content-Type: image/jpeg\r\n";
    header += "Content-Length: " + String(fb->len) + "\r\n\r\n";

    server.sendContent(header);
    client.write(fb->buf, fb->len);
    server.sendContent("\r\n");

    esp_camera_fb_return(fb);
    frameCount++;

    if (!client.connected()) break;

    // Small delay to prevent overwhelming the system
    delay(50);
  }

  Serial.printf("📹 Stream ended after %d frames\n", frameCount);
}

void handleCapture() {
  camera_fb_t * fb = esp_camera_fb_get();
  if (!fb) {
    server.send(500, "text/plain", "Camera capture failed");
    return;
  }

  server.sendHeader("Content-Disposition", "attachment; filename=capture.jpg");
  server.send_P(200, "image/jpeg", (const char *)fb->buf, fb->len);

  esp_camera_fb_return(fb);
}

void handleSettings() {
  String html = getSettingsPage();
  server.send(200, "text/html", html);
}

void handleStatus() {
  String status = "{\n";
  status += "  \"wifi_connected\": " + String(WiFi.status() == WL_CONNECTED ? "true" : "false") + ",\n";
  status += "  \"ip_address\": \"" + WiFi.localIP().toString() + "\",\n";
  status += "  \"rssi\": " + String(WiFi.RSSI()) + ",\n";
  status += "  \"free_heap\": " + String(ESP.getFreeHeap()) + ",\n";
  status += "  \"uptime\": " + String(millis()) + "\n";
  status += "}";

  server.send(200, "application/json", status);
}

void handleNotFound() {
  server.send(404, "text/plain", "Page not found");
}
