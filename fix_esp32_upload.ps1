# ESP32 Upload Fix Script
# This script will help fix the COM port and driver issues

Write-Host "ESP32 Upload Fix Script" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

# Step 1: Check current COM ports
Write-Host "`n1. Checking current COM ports..." -ForegroundColor Yellow
Get-WmiObject -Class Win32_SerialPort | Select-Object DeviceID, Description | Format-Table

# Step 2: Check for USB devices
Write-Host "`n2. Checking USB-to-Serial devices..." -ForegroundColor Yellow
Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Caption -match "(CP210|CH340|USB.*Serial|UART)"} | Select-Object Caption, DeviceID | Format-Table

# Step 3: Check device status
Write-Host "`n3. Checking device status..." -ForegroundColor Yellow
Get-PnpDevice | Where-Object {$_.FriendlyName -match "(CP210|CH340|USB.*Serial|UART)"} | Select-Object FriendlyName, Status | Format-Table

# Step 4: Download and install CP210x drivers
Write-Host "`n4. Downloading CP210x drivers..." -ForegroundColor Yellow
$driverUrl = "https://www.silabs.com/documents/public/software/CP210x_Windows_Drivers.zip"
$downloadPath = "$env:TEMP\CP210x_Drivers.zip"
$extractPath = "$env:TEMP\CP210x_Drivers"

try {
    # Download drivers
    Invoke-WebRequest -Uri $driverUrl -OutFile $downloadPath -UseBasicParsing
    Write-Host "Drivers downloaded successfully" -ForegroundColor Green
    
    # Extract drivers
    Expand-Archive -Path $downloadPath -DestinationPath $extractPath -Force
    Write-Host "Drivers extracted to: $extractPath" -ForegroundColor Green
    
    # Find the installer
    $installer = Get-ChildItem -Path $extractPath -Recurse -Name "*.exe" | Select-Object -First 1
    if ($installer) {
        $installerPath = Join-Path $extractPath $installer
        Write-Host "Found installer: $installerPath" -ForegroundColor Green
        Write-Host "Please run the installer manually with administrator privileges" -ForegroundColor Yellow
        Write-Host "Installer location: $installerPath" -ForegroundColor Cyan
    }
} catch {
    Write-Host "Error downloading drivers: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please download manually from: https://www.silabs.com/developers/usb-to-uart-bridge-vcp-drivers" -ForegroundColor Yellow
}

# Step 5: Instructions for manual steps
Write-Host "`n5. Manual steps required:" -ForegroundColor Yellow
Write-Host "   a) Disconnect your ESP32 from USB" -ForegroundColor White
Write-Host "   b) Install the CP210x drivers (run as administrator)" -ForegroundColor White
Write-Host "   c) Restart your computer" -ForegroundColor White
Write-Host "   d) Reconnect your ESP32 via USB" -ForegroundColor White
Write-Host "   e) Run this script again to check the new COM port" -ForegroundColor White

Write-Host "`n6. After driver installation, the correct COM port should appear" -ForegroundColor Green
Write-Host "   Use that COM port in Arduino IDE instead of COM3" -ForegroundColor Green

Write-Host "`nScript completed. Press any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
