# Deneyap Kart Camera WiFi Project

This project enables the Deneyap Kart 1A v2 to connect to WiFi and stream live video from the Deneyap Camera module through a web interface.

## Hardware Requirements

- **Deneyap Kart 1A v2** (ESP32-S3 based development board)
- **Deneyap Camera** (OV2640 sensor)
- USB-C cable for programming
- WiFi network access

## Features

- 📡 WiFi connection to "THE ALPHA" network
- 📹 Live camera streaming via web browser
- 📸 Photo capture and download
- ⚙️ Camera settings adjustment
- 📊 System status monitoring
- 📱 Mobile-responsive web interface

## Setup Instructions

### 1. Arduino IDE Setup

1. **Install Arduino IDE** (version 1.8.19 or later)
   - Download from: https://www.arduino.cc/en/software

2. **Add Deneyap Board Support**
   - Open Arduino IDE
   - Go to `File` → `Preferences`
   - Add this URL to "Additional Boards Manager URLs":
     ```
     https://raw.githubusercontent.com/deneyapkart/deneyapkart-arduino-core/master/package_deneyapkart_index.json
     ```
   - Go to `Tools` → `Board` → `Boards Manager`
   - Search for "Deneyap Development Cards"
   - Install the latest version

3. **Select Board and Port**
   - Go to `Tools` → `Board` → `Deneyap Development Cards` → `Deneyap Kart 1A v2`
   - Select the correct COM port under `Tools` → `Port`

### 2. Hardware Connection

1. **Connect Deneyap Camera**
   - Connect the Deneyap Camera to the FPC connector on the back of the Deneyap Kart 1A v2
   - Ensure the connector is properly seated and locked

2. **Power Connection**
   - Connect the Deneyap Kart to your computer via USB-C cable
   - The board will be powered through USB during development

### 3. Software Installation

1. **Download Project Files**
   - Download all project files:
     - `deneyap_camera_wifi.ino`
     - `camera_pins.h`
     - `web_interface.h`

2. **Open in Arduino IDE**
   - Open `deneyap_camera_wifi.ino` in Arduino IDE
   - The other files should automatically open as tabs

3. **Configure WiFi (if needed)**
   - The code is pre-configured for:
     - Network: "THE ALPHA"
     - Password: "20012001"
   - To change these, edit the following lines in the main .ino file:
     ```cpp
     const char* ssid = "YOUR_NETWORK_NAME";
     const char* password = "YOUR_PASSWORD";
     ```

4. **Upload Code**
   - Click the Upload button (→) in Arduino IDE
   - Wait for compilation and upload to complete

### 4. Usage

1. **Monitor Serial Output**
   - Open Serial Monitor (`Tools` → `Serial Monitor`)
   - Set baud rate to 115200
   - You should see connection status and IP address

2. **Access Web Interface**
   - Note the IP address displayed in Serial Monitor
   - Open a web browser and navigate to: `http://[IP_ADDRESS]`
   - Example: `http://*************`

3. **Web Interface Features**
   - **Live Stream**: View real-time camera feed
   - **Capture Photo**: Download current frame as JPEG
   - **Settings**: Adjust camera parameters
   - **Status**: View system information

## Troubleshooting

### Camera Issues
- **Camera initialization failed**: Check FPC connector connection
- **No image**: Verify camera module is properly connected
- **Poor image quality**: Adjust settings in the web interface

### WiFi Issues
- **Connection failed**: Verify network name and password
- **Weak signal**: Move closer to WiFi router
- **IP not displayed**: Check Serial Monitor for error messages

### Upload Issues
- **Board not detected**: Check USB cable and drivers
- **Compilation errors**: Ensure all files are in the same folder
- **Upload failed**: Try pressing the BOOT button during upload

## Pin Configuration

The camera pins are configured in `camera_pins.h`:

| Function | GPIO Pin |
|----------|----------|
| XCLK     | 15       |
| SIOD     | 4        |
| SIOC     | 5        |
| Y9       | 16       |
| Y8       | 17       |
| Y7       | 18       |
| Y6       | 12       |
| Y5       | 10       |
| Y4       | 8        |
| Y3       | 9        |
| Y2       | 11       |
| VSYNC    | 6        |
| HREF     | 7        |
| PCLK     | 13       |

## Technical Specifications

- **Microcontroller**: ESP32-S3 (240 MHz Dual Core)
- **Camera Sensor**: OV2640
- **WiFi**: 2.4 GHz 802.11b/g/n
- **Memory**: 512 KB SRAM, 8 MB PSRAM, 4 MB Flash
- **Max Resolution**: 1600x1200 (UXGA)
- **Streaming Format**: MJPEG over HTTP

## License

This project is open source and available under the MIT License.

## Support

For issues and questions:
- Check the troubleshooting section above
- Visit Deneyap documentation: https://docs.deneyapkart.org/
- Arduino ESP32 community forums
