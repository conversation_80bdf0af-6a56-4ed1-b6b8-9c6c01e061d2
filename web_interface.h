/*
 * Web Interface for Deneyap Camera WiFi Project
 * 
 * This file contains HTML templates and web interface functions
 * for the camera web server.
 */

#ifndef WEB_INTERFACE_H
#define WEB_INTERFACE_H

#include <Arduino.h>

// Main page HTML
String getMainPage() {
  String html = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deneyap Camera</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .camera-container {
            text-align: center;
            margin-bottom: 20px;
        }
        .camera-stream {
            max-width: 100%;
            border: 2px solid #ddd;
            border-radius: 5px;
        }
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-info {
            background-color: #17a2b8;
        }
        .btn-info:hover {
            background-color: #117a8b;
        }
        .status {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .status h3 {
            margin-top: 0;
            color: #495057;
        }
        @media (max-width: 600px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            .btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Deneyap Camera Control</h1>
        
        <div class="camera-container">
            <img src="/stream" class="camera-stream" alt="Camera Stream">
        </div>
        
        <div class="controls">
            <a href="/capture" class="btn btn-success" download="capture.jpg">📸 Capture Photo</a>
            <a href="/settings" class="btn btn-info">⚙️ Settings</a>
            <button onclick="refreshStream()" class="btn">🔄 Refresh Stream</button>
            <button onclick="checkStatus()" class="btn">📊 Status</button>
        </div>
        
        <div class="status" id="status">
            <h3>System Status</h3>
            <p>📡 WiFi: Connected to THE ALPHA</p>
            <p>📷 Camera: Active</p>
            <p>🌐 Server: Running</p>
        </div>
    </div>

    <script>
        function refreshStream() {
            const img = document.querySelector('.camera-stream');
            img.src = '/stream?' + new Date().getTime();
        }
        
        function checkStatus() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('status');
                    statusDiv.innerHTML = `
                        <h3>System Status</h3>
                        <p>📡 WiFi: ${data.wifi_connected ? 'Connected' : 'Disconnected'}</p>
                        <p>🌐 IP Address: ${data.ip_address}</p>
                        <p>📶 Signal Strength: ${data.rssi} dBm</p>
                        <p>💾 Free Memory: ${data.free_heap} bytes</p>
                        <p>⏱️ Uptime: ${Math.floor(data.uptime / 1000)} seconds</p>
                    `;
                })
                .catch(error => {
                    console.error('Error fetching status:', error);
                });
        }
        
        // Auto-refresh status every 30 seconds
        setInterval(checkStatus, 30000);
        
        // Initial status check
        checkStatus();
    </script>
</body>
</html>
)rawliteral";
  return html;
}

// Settings page HTML
String getSettingsPage() {
  String html = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Settings - Deneyap Camera</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .setting-group {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .setting-group h3 {
            margin-top: 0;
            color: #495057;
        }
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .setting-item:last-child {
            margin-bottom: 0;
        }
        label {
            font-weight: bold;
            color: #333;
        }
        input[type="range"] {
            width: 200px;
        }
        select {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .btn {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .controls {
            text-align: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚙️ Camera Settings</h1>
        
        <div class="setting-group">
            <h3>Image Quality</h3>
            <div class="setting-item">
                <label for="framesize">Frame Size:</label>
                <select id="framesize">
                    <option value="0">96x96</option>
                    <option value="1">160x120</option>
                    <option value="2">176x144</option>
                    <option value="3">240x176</option>
                    <option value="4">240x240</option>
                    <option value="5">320x240</option>
                    <option value="6">400x296</option>
                    <option value="7">480x320</option>
                    <option value="8" selected>640x480</option>
                    <option value="9">800x600</option>
                    <option value="10">1024x768</option>
                    <option value="11">1280x720</option>
                    <option value="12">1280x1024</option>
                    <option value="13">1600x1200</option>
                </select>
            </div>
            <div class="setting-item">
                <label for="quality">JPEG Quality:</label>
                <input type="range" id="quality" min="4" max="63" value="12">
                <span id="qualityValue">12</span>
            </div>
        </div>
        
        <div class="setting-group">
            <h3>Image Adjustments</h3>
            <div class="setting-item">
                <label for="brightness">Brightness:</label>
                <input type="range" id="brightness" min="-2" max="2" value="0">
                <span id="brightnessValue">0</span>
            </div>
            <div class="setting-item">
                <label for="contrast">Contrast:</label>
                <input type="range" id="contrast" min="-2" max="2" value="0">
                <span id="contrastValue">0</span>
            </div>
            <div class="setting-item">
                <label for="saturation">Saturation:</label>
                <input type="range" id="saturation" min="-2" max="2" value="0">
                <span id="saturationValue">0</span>
            </div>
        </div>
        
        <div class="setting-group">
            <h3>Camera Controls</h3>
            <div class="setting-item">
                <label for="hmirror">Horizontal Mirror:</label>
                <input type="checkbox" id="hmirror">
            </div>
            <div class="setting-item">
                <label for="vflip">Vertical Flip:</label>
                <input type="checkbox" id="vflip">
            </div>
        </div>
        
        <div class="controls">
            <button onclick="applySettings()" class="btn">Apply Settings</button>
            <button onclick="resetSettings()" class="btn btn-secondary">Reset to Default</button>
            <a href="/" class="btn btn-secondary">← Back to Camera</a>
        </div>
    </div>

    <script>
        // Update range value displays
        document.getElementById('quality').addEventListener('input', function() {
            document.getElementById('qualityValue').textContent = this.value;
        });
        
        document.getElementById('brightness').addEventListener('input', function() {
            document.getElementById('brightnessValue').textContent = this.value;
        });
        
        document.getElementById('contrast').addEventListener('input', function() {
            document.getElementById('contrastValue').textContent = this.value;
        });
        
        document.getElementById('saturation').addEventListener('input', function() {
            document.getElementById('saturationValue').textContent = this.value;
        });
        
        function applySettings() {
            alert('Settings applied! (Note: This is a demo - actual implementation would send settings to the camera)');
        }
        
        function resetSettings() {
            document.getElementById('framesize').value = '8';
            document.getElementById('quality').value = '12';
            document.getElementById('brightness').value = '0';
            document.getElementById('contrast').value = '0';
            document.getElementById('saturation').value = '0';
            document.getElementById('hmirror').checked = false;
            document.getElementById('vflip').checked = false;
            
            // Update value displays
            document.getElementById('qualityValue').textContent = '12';
            document.getElementById('brightnessValue').textContent = '0';
            document.getElementById('contrastValue').textContent = '0';
            document.getElementById('saturationValue').textContent = '0';
        }
    </script>
</body>
</html>
)rawliteral";
  return html;
}

#endif // WEB_INTERFACE_H
