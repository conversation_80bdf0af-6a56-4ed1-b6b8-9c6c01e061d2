/*
 * Camera Diagnostic Tool for Deneyap Kart
 * This will systematically test different pin configurations to find your camera
 */

#include <Wire.h>

// Different pin combinations to try for Deneyap Kart
struct PinConfig {
  int sda;
  int scl;
  const char* name;
};

PinConfig pinConfigs[] = {
  // Original configurations from your camera_pins.h
  {4, 5, "Original Deneyap Config"},
  {26, 27, "ESP32-WROVER Config"},
  {18, 23, "ESP32-S3 Config (Current)"},

  // Standard ESP32 I2C pins
  {21, 22, "Standard ESP32 I2C"},
  {14, 15, "Alternative ESP32 I2C"},

  // Other common camera configurations
  {25, 26, "ESP32-CAM AI-Thinker"},
  {13, 14, "Custom Config 1"},
  {16, 17, "Custom Config 2"},
  {19, 20, "Custom Config 3"},
  {32, 33, "Custom Config 4"},

  // Deneyap specific attempts
  {6, 7, "Deneyap FPC Config 1"},
  {8, 9, "Deneyap FPC Config 2"},
  {10, 11, "Deneyap FPC Config 3"},
  {12, 13, "Deneyap FPC Config 4"}
};

void setup() {
  Serial.begin(115200);
  delay(2000);

  Serial.println("\n==========================================");
  Serial.println("🔍 CAMERA DIAGNOSTIC TOOL FOR DENEYAP KART");
  Serial.println("==========================================");
  Serial.println("This tool will test different I2C pin combinations");
  Serial.println("to find your camera module.\n");

  Serial.println("📋 HARDWARE CHECK:");
  Serial.println("1. ✅ Make sure camera FFC cable is connected");
  Serial.println("2. ✅ Verify camera module is properly seated");
  Serial.println("3. ✅ Check for any loose connections");
  Serial.println("4. ✅ Ensure camera module is not damaged\n");

  int numConfigs = sizeof(pinConfigs) / sizeof(pinConfigs[0]);
  bool cameraFound = false;

  Serial.printf("🔄 Testing %d different pin configurations...\n\n", numConfigs);

  for (int i = 0; i < numConfigs; i++) {
    Serial.printf("Test %d/%d: %s\n", i+1, numConfigs, pinConfigs[i].name);
    Serial.printf("         SDA: GPIO %d, SCL: GPIO %d\n",
                  pinConfigs[i].sda, pinConfigs[i].scl);

    // Initialize I2C with current pin configuration
    Wire.begin(pinConfigs[i].sda, pinConfigs[i].scl);
    Wire.setClock(100000); // 100kHz for better compatibility
    delay(100);

    bool deviceFound = false;

    // Scan I2C addresses (focus on camera addresses)
    for (byte address = 0x08; address < 0x78; address++) {
      Wire.beginTransmission(address);
      byte error = Wire.endTransmission();

      if (error == 0) {
        Serial.printf("         📍 Device found at address 0x%02X", address);

        // Check for known camera addresses
        if (address == 0x30) {
          Serial.print(" 🎥 OV2640 CAMERA DETECTED!");
          cameraFound = true;
        } else if (address == 0x21) {
          Serial.print(" 📷 OV7670 Camera");
        } else if (address == 0x3C) {
          Serial.print(" 📺 OLED Display");
        } else if (address == 0x68) {
          Serial.print(" ⏰ RTC/IMU");
        }
        Serial.println();
        deviceFound = true;
      }
    }

    if (!deviceFound) {
      Serial.println("         ❌ No devices found");
    }

    Wire.end();
    Serial.println();
    delay(500);
  }

  Serial.println("==========================================");
  if (cameraFound) {
    Serial.println("🎉 SUCCESS! Camera found!");
    Serial.println("📝 Copy the working SDA/SCL pins to your camera_pins.h");
    Serial.println("   Update SIOD_GPIO_NUM and SIOC_GPIO_NUM with these values");
  } else {
    Serial.println("❌ Camera not detected on any pin combination");
    Serial.println("💡 Possible issues:");
    Serial.println("   - Camera module is not connected");
    Serial.println("   - FFC cable is damaged or loose");
    Serial.println("   - Camera module is faulty");
    Serial.println("   - Different pin configuration needed");
    Serial.println("   - Power supply issue");
  }
  Serial.println("==========================================");
}

void loop() {
  // Additional diagnostic - check if camera gets power
  static unsigned long lastCheck = 0;
  if (millis() - lastCheck > 5000) {
    lastCheck = millis();

    Serial.println("\n🔋 POWER TEST:");
    Serial.println("If camera has power, you might see a small LED on the camera module");
    Serial.println("or feel slight warmth. Check your camera module now.");
    Serial.println("Press RESET button to run scan again if needed.\n");
  }

  // Blink built-in LED to show the scan is complete
  digitalWrite(2, HIGH);
  delay(500);
  digitalWrite(2, LOW);
  delay(500);
}
